// Firestore Manager for Permanent Data Storage
// This replaces localStorage with Firebase Firestore for permanent data persistence

class FirestoreManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
    }

    // Initialize Firestore connection
    async initialize() {
        if (window.firebaseDB && window.firestoreFunctions) {
            this.db = window.firebaseDB;
            this.functions = window.firestoreFunctions;
            this.isInitialized = true;
            console.log('Firestore Manager initialized successfully');
            return true;
        } else {
            console.error('Firebase Firestore not available');
            return false;
        }
    }

    // Save menu items to Firestore
    async saveMenuItems(menuItems) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'menuItems');
            await this.functions.setDoc(docRef, {
                items: menuItems,
                lastUpdated: new Date().toISOString(),
                updatedBy: sessionStorage.getItem('adminEmail') || 'unknown'
            });
            
            console.log('Menu items saved to Firestore successfully');
            
            // Also save to localStorage as backup
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            
            return true;
        } catch (error) {
            console.error('Error saving menu items to Firestore:', error);
            
            // Fallback to localStorage
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            throw error;
        }
    }

    // Load menu items from Firestore
    async loadMenuItems() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'menuItems');
            const docSnap = await this.functions.getDoc(docRef);
            
            if (docSnap.exists()) {
                const data = docSnap.data();
                console.log('Menu items loaded from Firestore successfully');
                
                // Save to localStorage as cache
                localStorage.setItem('menuItems', JSON.stringify(data.items));
                
                return data.items;
            } else {
                console.log('No menu items found in Firestore, using default data');
                return null;
            }
        } catch (error) {
            console.error('Error loading menu items from Firestore:', error);
            
            // Fallback to localStorage
            const localData = localStorage.getItem('menuItems');
            if (localData) {
                console.log('Using localStorage fallback');
                return JSON.parse(localData);
            }
            
            return null;
        }
    }

    // Save restaurant settings to Firestore
    async saveSettings(settings) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'settings');
            await this.functions.setDoc(docRef, {
                ...settings,
                lastUpdated: new Date().toISOString(),
                updatedBy: sessionStorage.getItem('adminEmail') || 'unknown'
            });
            
            console.log('Settings saved to Firestore successfully');
            
            // Also save to localStorage as backup
            localStorage.setItem('settings', JSON.stringify(settings));
            
            return true;
        } catch (error) {
            console.error('Error saving settings to Firestore:', error);
            
            // Fallback to localStorage
            localStorage.setItem('settings', JSON.stringify(settings));
            throw error;
        }
    }

    // Load restaurant settings from Firestore
    async loadSettings() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'settings');
            const docSnap = await this.functions.getDoc(docRef);
            
            if (docSnap.exists()) {
                const data = docSnap.data();
                console.log('Settings loaded from Firestore successfully');
                
                // Save to localStorage as cache
                localStorage.setItem('settings', JSON.stringify(data));
                
                return data;
            } else {
                console.log('No settings found in Firestore');
                return {};
            }
        } catch (error) {
            console.error('Error loading settings from Firestore:', error);
            
            // Fallback to localStorage
            const localData = localStorage.getItem('settings');
            if (localData) {
                console.log('Using localStorage fallback for settings');
                return JSON.parse(localData);
            }
            
            return {};
        }
    }

    // Initialize default menu items if none exist
    async initializeDefaultMenuItems() {
        const existingItems = await this.loadMenuItems();
        
        if (!existingItems || Object.keys(existingItems).length === 0) {
            console.log('Initializing default menu items in Firestore...');
            
            const defaultMenuItems = {
                appetizers: [
                    {
                        nameEn: "Hummus",
                        nameAr: "حمص",
                        descEn: "Creamy chickpea dip with tahini and olive oil",
                        descAr: "حمص كريمي مع طحينة وزيت زيتون",
                        price: "25",
                        image: "../images/hummus.jpg"
                    },
                    {
                        nameEn: "Baba Ganoush",
                        nameAr: "بابا غنوج",
                        descEn: "Smoky roasted eggplant dip with garlic and lemon",
                        descAr: "غموس الباذنجان المشوي مع الثوم والليمون",
                        price: "28",
                        image: "../images/baba-ganoush.jpg"
                    }
                ],
                main: [
                    {
                        nameEn: "Lamb Chops",
                        nameAr: "ريش لحم غنم",
                        descEn: "Marinated lamb chops with mint sauce",
                        descAr: "ريش ضأن متبلة مع صلصة النعناع",
                        price: "95",
                        image: "../images/lamb-chops.jpg"
                    }
                ],
                seafood: [],
                desserts: [],
                salads: [],
                soups: [],
                sandwiches: [],
                breakfast: [],
                specials: [],
                beverages: []
            };
            
            await this.saveMenuItems(defaultMenuItems);
            return defaultMenuItems;
        }
        
        return existingItems;
    }
}

// Create global instance
window.firestoreManager = new FirestoreManager();

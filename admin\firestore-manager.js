// Firestore Manager for Permanent Data Storage
// This replaces localStorage with Firebase Firestore for permanent data persistence

class FirestoreManager {
    constructor() {
        this.db = null;
        this.storage = null;
        this.isInitialized = false;
    }

    // Initialize Firestore and Storage connections
    async initialize() {
        if (window.firebaseDB && window.firestoreFunctions && window.firebaseStorage && window.storageFunctions) {
            this.db = window.firebaseDB;
            this.storage = window.firebaseStorage;
            this.functions = window.firestoreFunctions;
            this.storageFunctions = window.storageFunctions;
            this.isInitialized = true;
            console.log('Firestore Manager initialized successfully with Storage support');
            return true;
        } else {
            console.error('Firebase Firestore or Storage not available');
            return false;
        }
    }

    // Upload image to Firebase Storage and return download URL
    async uploadImage(file, path = 'menu-images') {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Create a unique filename
            const timestamp = Date.now();
            const fileName = `${timestamp}_${file.name}`;
            const storageRef = this.storageFunctions.ref(this.storage, `${path}/${fileName}`);

            // Upload the file
            console.log('Uploading image to Firebase Storage...');
            const snapshot = await this.storageFunctions.uploadBytes(storageRef, file);

            // Get the download URL
            const downloadURL = await this.storageFunctions.getDownloadURL(snapshot.ref);
            console.log('Image uploaded successfully:', downloadURL);

            return downloadURL;
        } catch (error) {
            console.error('Error uploading image to Firebase Storage:', error);
            throw error;
        }
    }

    // Delete image from Firebase Storage
    async deleteImage(imageUrl) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Extract the path from the URL
            const url = new URL(imageUrl);
            const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
            if (pathMatch) {
                const imagePath = decodeURIComponent(pathMatch[1]);
                const imageRef = this.storageFunctions.ref(this.storage, imagePath);
                await this.storageFunctions.deleteObject(imageRef);
                console.log('Image deleted from Firebase Storage:', imagePath);
            }
        } catch (error) {
            console.error('Error deleting image from Firebase Storage:', error);
            // Don't throw error for deletion failures as it's not critical
        }
    }

    // Save menu items to Firestore
    async saveMenuItems(menuItems) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'menuItems');
            await this.functions.setDoc(docRef, {
                items: menuItems,
                lastUpdated: new Date().toISOString(),
                updatedBy: sessionStorage.getItem('adminEmail') || 'unknown'
            });
            
            console.log('Menu items saved to Firestore successfully');
            
            // Also save to localStorage as backup
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            
            return true;
        } catch (error) {
            console.error('Error saving menu items to Firestore:', error);
            
            // Fallback to localStorage
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            throw error;
        }
    }

    // Load menu items from Firestore
    async loadMenuItems() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'menuItems');
            const docSnap = await this.functions.getDoc(docRef);
            
            if (docSnap.exists()) {
                const data = docSnap.data();
                console.log('Menu items loaded from Firestore successfully');
                
                // Save to localStorage as cache
                localStorage.setItem('menuItems', JSON.stringify(data.items));
                
                return data.items;
            } else {
                console.log('No menu items found in Firestore, using default data');
                return null;
            }
        } catch (error) {
            console.error('Error loading menu items from Firestore:', error);
            
            // Fallback to localStorage
            const localData = localStorage.getItem('menuItems');
            if (localData) {
                console.log('Using localStorage fallback');
                return JSON.parse(localData);
            }
            
            return null;
        }
    }

    // Save restaurant settings to Firestore
    async saveSettings(settings) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'settings');
            await this.functions.setDoc(docRef, {
                ...settings,
                lastUpdated: new Date().toISOString(),
                updatedBy: sessionStorage.getItem('adminEmail') || 'unknown'
            });
            
            console.log('Settings saved to Firestore successfully');
            
            // Also save to localStorage as backup
            localStorage.setItem('settings', JSON.stringify(settings));
            
            return true;
        } catch (error) {
            console.error('Error saving settings to Firestore:', error);
            
            // Fallback to localStorage
            localStorage.setItem('settings', JSON.stringify(settings));
            throw error;
        }
    }

    // Load restaurant settings from Firestore
    async loadSettings() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const docRef = this.functions.doc(this.db, 'restaurant', 'settings');
            const docSnap = await this.functions.getDoc(docRef);

            if (docSnap.exists()) {
                const data = docSnap.data();
                console.log('Settings loaded from Firestore successfully');

                // Remove metadata fields before returning
                const cleanData = { ...data };
                delete cleanData.lastUpdated;
                delete cleanData.updatedBy;

                // Save to localStorage as cache
                localStorage.setItem('settings', JSON.stringify(cleanData));

                return cleanData;
            } else {
                console.log('No settings found in Firestore, initializing with defaults');

                // Return default settings
                const defaultSettings = {
                    restaurantNameEn: 'Al-Andalus Restaurant',
                    restaurantNameAr: 'مطعم الأندلس',
                    contactPhone: '+966 12 534 0000',
                    contactAddressEn: 'Ibrahim El Khalil Street, Al Haram, Makkah 21955, Saudi Arabia',
                    contactAddressAr: 'شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية'
                };

                // Save defaults to Firestore
                await this.saveSettings(defaultSettings);

                return defaultSettings;
            }
        } catch (error) {
            console.error('Error loading settings from Firestore:', error);

            // Fallback to localStorage
            const localData = localStorage.getItem('settings');
            if (localData) {
                console.log('Using localStorage fallback for settings');
                return JSON.parse(localData);
            }

            // Return defaults if everything fails
            return {
                restaurantNameEn: 'Al-Andalus Restaurant',
                restaurantNameAr: 'مطعم الأندلس',
                contactPhone: '+966 12 534 0000',
                contactAddressEn: 'Ibrahim El Khalil Street, Al Haram, Makkah 21955, Saudi Arabia',
                contactAddressAr: 'شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية'
            };
        }
    }

    // Initialize default menu items if none exist
    async initializeDefaultMenuItems() {
        const existingItems = await this.loadMenuItems();
        
        if (!existingItems || Object.keys(existingItems).length === 0) {
            console.log('Initializing default menu items in Firestore...');
            
            const defaultMenuItems = {
                appetizers: [
                    {
                        nameEn: "Hummus",
                        nameAr: "حمص",
                        descEn: "Creamy chickpea dip with tahini and olive oil",
                        descAr: "حمص كريمي مع طحينة وزيت زيتون",
                        price: "25",
                        image: "../images/hummus.jpg"
                    },
                    {
                        nameEn: "Baba Ganoush",
                        nameAr: "بابا غنوج",
                        descEn: "Smoky roasted eggplant dip with garlic and lemon",
                        descAr: "غموس الباذنجان المشوي مع الثوم والليمون",
                        price: "28",
                        image: "../images/baba-ganoush.jpg"
                    }
                ],
                main: [
                    {
                        nameEn: "Lamb Chops",
                        nameAr: "ريش لحم غنم",
                        descEn: "Marinated lamb chops with mint sauce",
                        descAr: "ريش ضأن متبلة مع صلصة النعناع",
                        price: "95",
                        image: "../images/lamb-chops.jpg"
                    }
                ],
                seafood: [],
                desserts: [],
                salads: [],
                soups: [],
                sandwiches: [],
                breakfast: [],
                specials: [],
                beverages: []
            };
            
            await this.saveMenuItems(defaultMenuItems);
            return defaultMenuItems;
        }
        
        return existingItems;
    }
}

// Create global instance
window.firestoreManager = new FirestoreManager();

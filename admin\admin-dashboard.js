document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in with Firebase and is an approved admin
    if (!sessionStorage.getItem('adminLoggedIn') ||
        !sessionStorage.getItem('adminEmail') ||
        !sessionStorage.getItem('isApprovedAdmin')) {

        console.log('Unauthorized access attempt to admin dashboard');
        alert('Access denied. You are not authorized to access the admin dashboard.');
        window.location.href = '../index.html';
        return;
    }

    // Display current admin email
    const adminEmail = sessionStorage.getItem('adminEmail');
    console.log('Admin dashboard loaded for approved admin:', adminEmail);

    // Initialize Firestore and load menu items
    initializeFirestoreData();

    // Debug: Check localStorage content
    console.log('Current localStorage menuItems:', localStorage.getItem('menuItems'));

    // Set default language
    let currentLang = localStorage.getItem('adminLang') || 'ar';
    document.documentElement.lang = currentLang;

    // Update active language button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${currentLang}-btn`).classList.add('active');

    // Tab switching functionality
    const tabLinks = document.querySelectorAll('.admin-nav li');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Remove active class from all tabs
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding content
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Logout functionality
    document.getElementById('logout-btn').addEventListener('click', function() {
        // Clear session storage
        sessionStorage.removeItem('adminLoggedIn');
        sessionStorage.removeItem('adminEmail');

        // Redirect to main page
        window.location.href = '../index.html';
    });

    // Load menu items for selected category
    const categorySelect = document.getElementById('category-select');
    categorySelect.addEventListener('change', loadMenuItems);

    // Initial load of menu items
    loadMenuItems();

    // Add new item button
    document.querySelector('.add-item-btn').addEventListener('click', function() {
        openItemEditor();
    });

    // Save changes button
    document.querySelector('.save-changes-btn').addEventListener('click', saveMenuChanges);

    // Modal close button
    document.querySelector('.close-modal').addEventListener('click', function() {
        document.getElementById('item-editor-modal').style.display = 'none';
    });

    // Item editor form
    document.getElementById('item-editor-form').addEventListener('submit', saveMenuItem);

    // Item image file input change
    document.getElementById('item-image-file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const fileName = file.name;
            document.getElementById('item-file-name').innerHTML = `
                <span class="en">${fileName}</span>
                <span class="ar">${fileName}</span>
            `;

            const reader = new FileReader();
            reader.onload = function(event) {
                const imageData = event.target.result;
                document.getElementById('item-image-data').value = imageData;

                const preview = document.getElementById('item-image-preview');
                const previewImg = document.getElementById('item-preview-img');
                previewImg.src = imageData;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('item-file-name').innerHTML = `
                <span class="en">No file selected</span>
                <span class="ar">لم يتم اختيار ملف</span>
            `;
            document.getElementById('item-image-preview').style.display = 'none';
        }
    });

    // Settings form
    document.getElementById('settings-form').addEventListener('submit', saveSettings);

    // Load settings
    loadSettings();

    // Update category select based on current language
    updateCategorySelect();
});

// Function to initialize Firestore data
async function initializeFirestoreData() {
    try {
        console.log('Initializing Firestore data...');

        // Wait for Firestore manager to be ready
        if (window.firestoreManager) {
            await window.firestoreManager.initialize();

            // Load menu items from Firestore
            const menuItems = await window.firestoreManager.initializeDefaultMenuItems();
            console.log('Menu items loaded from Firestore:', menuItems);

            // Load settings from Firestore
            const settings = await window.firestoreManager.loadSettings();
            console.log('Settings loaded from Firestore:', settings);

            // Load the current category
            loadMenuItems();
            loadSettings();
        } else {
            console.log('Firestore manager not available, falling back to localStorage');
            initializeMenuItems();
        }
    } catch (error) {
        console.error('Error initializing Firestore data:', error);
        console.log('Falling back to localStorage initialization');
        initializeMenuItems();
    }
}

// Function to initialize menu items from existing data (fallback)
function initializeMenuItems() {
    const existingMenuItems = localStorage.getItem('menuItems');

    // If localStorage is empty or has no items, populate with default data
    if (!existingMenuItems || Object.keys(JSON.parse(existingMenuItems)).length === 0) {
        console.log('Initializing menu items with default data...');

        const defaultMenuItems = {
            appetizers: [
                {
                    nameEn: "Hummus",
                    nameAr: "حمص",
                    descEn: "Creamy chickpea dip with tahini and olive oil",
                    descAr: "حمص كريمي مع طحينة وزيت زيتون",
                    price: "25",
                    image: "../images/hummus.jpg"
                },
                {
                    nameEn: "Baba Ganoush",
                    nameAr: "بابا غنوج",
                    descEn: "Smoky roasted eggplant dip with garlic and lemon",
                    descAr: "غموس الباذنجان المشوي مع الثوم والليمون",
                    price: "28",
                    image: "../images/baba-ganoush.jpg"
                },
                {
                    nameEn: "Tabbouleh",
                    nameAr: "تبولة",
                    descEn: "Fresh parsley salad with tomatoes, mint, and bulgur",
                    descAr: "سلطة البقدونس الطازجة مع الطماطم والنعناع والبرغل",
                    price: "22",
                    image: "../images/tabbouleh.jpg"
                },
                {
                    nameEn: "Fattoush",
                    nameAr: "فتوش",
                    descEn: "Mixed greens with crispy pita bread and sumac dressing",
                    descAr: "خضار مشكلة مع خبز البيتا المقرمش وتتبيلة السماق",
                    price: "26",
                    image: "../images/fattoush.jpg"
                },
                {
                    nameEn: "Labneh",
                    nameAr: "لبنة",
                    descEn: "Strained yogurt with olive oil and za'atar",
                    descAr: "لبن مصفى مع زيت زيتون وزعتر",
                    price: "18",
                    image: "../images/labneh.jpg"
                },
                {
                    nameEn: "Kibbeh",
                    nameAr: "كبة",
                    descEn: "Fried bulgur shells stuffed with spiced meat",
                    descAr: "أقراص البرغل المقلية المحشوة باللحم المتبل",
                    price: "30",
                    image: "../images/kibbeh.jpg"
                },
                {
                    nameEn: "Makdous",
                    nameAr: "مكدوس",
                    descEn: "Pickled eggplants stuffed with walnuts and garlic",
                    descAr: "باذنجان مخلل محشو بالجوز والثوم",
                    price: "24",
                    image: "../images/makdous.jpg"
                },
                {
                    nameEn: "Cheese Sambousek",
                    nameAr: "سمبوسك بالجبنة",
                    descEn: "Fried pastry stuffed with white cheese",
                    descAr: "معجنات مقلية محشوة بالجبن الأبيض",
                    price: "32",
                    image: "../images/sambousek.jpg"
                }
            ],
            main: [
                {
                    nameEn: "Lamb Chops",
                    nameAr: "ريش لحم غنم",
                    descEn: "Marinated lamb chops with mint sauce",
                    descAr: "ريش ضأن متبلة مع صلصة النعناع",
                    price: "95",
                    image: "../images/lamb-chops.jpg"
                },
                {
                    nameEn: "Chicken Kabsa",
                    nameAr: "كبسة دجاج",
                    descEn: "Traditional rice dish with spiced chicken",
                    descAr: "طبق أرز تقليدي مع دجاج متبل",
                    price: "65",
                    image: "../images/chicken-kabsa.jpg"
                },
                {
                    nameEn: "Mixed Grill",
                    nameAr: "مشاوي مشكلة",
                    descEn: "Assorted grilled meats with rice and vegetables",
                    descAr: "لحوم مشوية متنوعة مع أرز وخضار",
                    price: "85",
                    image: "../images/mixed-grill.jpg"
                },
                {
                    nameEn: "Mansaf",
                    nameAr: "منسف",
                    descEn: "Traditional lamb dish with yogurt sauce and rice",
                    descAr: "طبق لحم ضأن تقليدي مع صلصة اللبن والأرز",
                    price: "90",
                    image: "../images/mansaf.jpg"
                },
                {
                    nameEn: "Shawarma Plate",
                    nameAr: "طبق شاورما",
                    descEn: "Sliced marinated meat with garlic sauce and rice",
                    descAr: "لحم متبل مقطع مع صلصة الثوم والأرز",
                    price: "55",
                    image: "../images/shawarma-plate.jpg"
                }
            ],
            seafood: [
                {
                    nameEn: "Grilled Sea Bass",
                    nameAr: "سمك قاروص مشوي",
                    descEn: "Whole sea bass grilled with herbs and lemon",
                    descAr: "سمك قاروص كامل مشوي مع الأعشاب والليمون",
                    price: "95",
                    image: "../images/sea-bass.jpg"
                },
                {
                    nameEn: "Shrimp Kabsa",
                    nameAr: "كبسة روبيان",
                    descEn: "Traditional rice dish with jumbo shrimp",
                    descAr: "طبق أرز تقليدي مع روبيان جامبو",
                    price: "85",
                    image: "../images/shrimp-kabsa.jpg"
                },
                {
                    nameEn: "Calamari",
                    nameAr: "كاليماري",
                    descEn: "Fried squid rings with garlic aioli",
                    descAr: "حلقات الحبار المقلية مع صلصة الثوم",
                    price: "45",
                    image: "../images/calamari.jpg"
                }
            ],
            desserts: [
                {
                    nameEn: "Kunafa",
                    nameAr: "كنافة",
                    descEn: "Traditional sweet cheese pastry soaked in sweet syrup",
                    descAr: "حلوى تقليدية من الجبن والعجين المغمور",
                    price: "30",
                    image: "../images/desserts/kunafa.jpg"
                },
                {
                    nameEn: "Baklava",
                    nameAr: "بقلاوة",
                    descEn: "Layered pastry with nuts and honey syrup",
                    descAr: "معجنات مطبقة مع المكسرات وشراب العسل",
                    price: "25",
                    image: "../images/desserts/baklava.jpg"
                }
            ],
            salads: [
                {
                    nameEn: "Halloumi",
                    nameAr: "حلومي",
                    descEn: "Grilled cheese with olive oil and mint",
                    descAr: "جبنة مشوية مع زيت زيتون ونعناع",
                    price: "32",
                    image: "../images/halloumi.jpg"
                }
            ],
            soups: [
                {
                    nameEn: "Lentil Soup",
                    nameAr: "شوربة عدس",
                    descEn: "Traditional lentil soup with cumin and lemon",
                    descAr: "شوربة عدس تقليدية مع كمون وليمون",
                    price: "20",
                    image: "../images/lentil-soup.jpg"
                }
            ],
            sandwiches: [
                {
                    nameEn: "Shawarma Sandwich",
                    nameAr: "ساندويش شاورما",
                    descEn: "Grilled marinated meat with garlic sauce in pita bread",
                    descAr: "لحم متبل مشوي مع صلصة الثوم في خبز بيتا",
                    price: "30",
                    image: "../images/shawarma.jpg"
                }
            ],
            breakfast: [
                {
                    nameEn: "Foul Medames",
                    nameAr: "فول مدمس",
                    descEn: "Stewed fava beans with olive oil and spices",
                    descAr: "فول مطبوخ مع زيت زيتون وتوابل",
                    price: "25",
                    image: "../images/foul.jpg"
                }
            ],
            specials: [
                {
                    nameEn: "Chef's Special Kabsa",
                    nameAr: "كبسة الشيف الخاصة",
                    descEn: "Premium rice dish with lamb and special spice blend",
                    descAr: "طبق أرز فاخر مع لحم ضأن وخلطة توابل خاصة",
                    price: "90",
                    image: "../images/special-kabsa.jpg"
                }
            ],
            beverages: []
        };

        localStorage.setItem('menuItems', JSON.stringify(defaultMenuItems));
        console.log('Menu items initialized successfully!');
        showSuccessMessage('Menu items loaded successfully! You can now view and edit them.');
    } else {
        console.log('Menu items already exist in localStorage');
    }
}

// Debug function to check localStorage data
function debugMenuItems() {
    const menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
    console.log('Current menu items in localStorage:', menuItems);

    for (const category in menuItems) {
        console.log(`${category}: ${menuItems[category].length} items`);
    }

    return menuItems;
}

// Function to clear all menu items (for debugging)
function clearAllMenuItems() {
    if (confirm('Are you sure you want to clear all menu items? This action cannot be undone.')) {
        localStorage.removeItem('menuItems');
        console.log('All menu items cleared');
        showSuccessMessage('All menu items cleared. Refresh the page to reload default items.');
        loadMenuItems();
    }
}

// Language switcher function
function switchLanguage(lang) {
    document.documentElement.lang = lang;
    localStorage.setItem('adminLang', lang);

    // Update active button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${lang}-btn`).classList.add('active');

    // Update category select
    updateCategorySelect();
}

// Update the category select dropdown based on language
function updateCategorySelect() {
    const categorySelect = document.getElementById('category-select');
    const currentLang = document.documentElement.lang;

    // Hide all language-specific options and show only the current language
    const options = categorySelect.querySelectorAll('option');
    options.forEach(option => {
        if (option.classList.contains('en') || option.classList.contains('ar')) {
            option.style.display = 'none';
        }
        if (option.classList.contains(currentLang)) {
            option.style.display = '';
        }
    });
}

// Function to show success message
function showSuccessMessage(message) {
    const successMessage = document.getElementById('success-message');
    successMessage.textContent = message;
    successMessage.classList.add('show');

    setTimeout(() => {
        successMessage.classList.remove('show');
    }, 5000);
}

// Function to load menu items for selected category
function loadMenuItems() {
    const category = document.getElementById('category-select').value;
    const menuItemsContainer = document.querySelector('.menu-items-editor');

    console.log(`Loading menu items for category: ${category}`);

    // Clear current items
    menuItemsContainer.innerHTML = '';

    try {
        // Load items from localStorage or use default items
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};

        // Ensure the category exists and is an array
        if (!Array.isArray(menuItems[category])) {
            menuItems[category] = [];
            // Save the updated structure back to localStorage
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
        }

        console.log(`Found ${menuItems[category].length} items in ${category} category`);

        // Display items
        if (menuItems[category].length === 0) {
            menuItemsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-utensils"></i>
                    <p class="en">No items in this category yet. Click "Add New Item" to create one.</p>
                    <p class="ar">لا توجد عناصر في هذه الفئة حتى الآن. انقر على "إضافة عنصر جديد" لإنشاء واحد.</p>
                </div>
            `;
        } else {
            menuItems[category].forEach((item, index) => {
                const itemCard = document.createElement('div');
                itemCard.className = 'menu-item-card';
                itemCard.innerHTML = `
                    <div class="menu-item-image">
                        <img src="${item.image && item.image.trim() ? item.image : '../images/fallback.jpg'}"
                             alt="${item.nameEn || 'Menu item'}"
                             onerror="this.src='../images/fallback.jpg'">
                    </div>
                    <div class="menu-item-info">
                        <h3 class="en">${item.nameEn || 'No English name'}</h3>
                        <h3 class="ar">${item.nameAr || 'لا يوجد اسم عربي'}</h3>
                        <p class="en">${item.descEn || 'No English description'}</p>
                        <p class="ar">${item.descAr || 'لا يوجد وصف عربي'}</p>
                        <p><strong>${item.price || 0} SAR</strong></p>
                    </div>
                    <div class="menu-item-actions">
                        <button class="edit-btn" data-index="${index}" title="Edit item">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-btn" data-index="${index}" title="Delete item">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                menuItemsContainer.appendChild(itemCard);
            });
        }

        // Add event listeners to edit and delete buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                openItemEditor(category, index);
            });
        });

        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                deleteMenuItem(category, index);
            });
        });

    } catch (error) {
        console.error('Error loading menu items:', error);
        menuItemsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                <p class="en">Error loading menu items. Please refresh the page or contact support.</p>
                <p class="ar">خطأ في تحميل عناصر القائمة. يرجى تحديث الصفحة أو الاتصال بالدعم.</p>
                <button onclick="initializeMenuItems(); loadMenuItems();" class="save-changes-btn" style="margin-top: 15px;">
                    <span class="en">Reset Menu Items</span>
                    <span class="ar">إعادة تعيين عناصر القائمة</span>
                </button>
            </div>
        `;
    }
}

// Function to open item editor modal
function openItemEditor(category, index) {
    const modal = document.getElementById('item-editor-modal');
    const form = document.getElementById('item-editor-form');

    // Clear form
    form.reset();
    document.getElementById('item-image-preview').style.display = 'none';
    document.getElementById('item-image-data').value = '';
    document.getElementById('item-file-name').innerHTML = `
        <span class="en">No file selected</span>
        <span class="ar">لم يتم اختيار ملف</span>
    `;

    // Set modal title based on whether we're adding or editing
    if (category && index !== undefined) {
        document.getElementById('modal-title').textContent = 'Edit Menu Item';
        document.getElementById('modal-title-ar').textContent = 'تعديل عنصر القائمة';
    } else {
        document.getElementById('modal-title').textContent = 'Add New Menu Item';
        document.getElementById('modal-title-ar').textContent = 'إضافة عنصر جديد';
    }

    // If editing existing item
    if (category && index !== undefined) {
        const menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
        if (menuItems[category] && menuItems[category][index]) {
            const item = menuItems[category][index];

            document.getElementById('item-name-en').value = item.nameEn || '';
            document.getElementById('item-name-ar').value = item.nameAr || '';
            document.getElementById('item-desc-en').value = item.descEn || '';
            document.getElementById('item-desc-ar').value = item.descAr || '';
            document.getElementById('item-price').value = item.price || '';

            // Show image preview if available
            if (item.image) {
                const preview = document.getElementById('item-image-preview');
                const previewImg = document.getElementById('item-preview-img');
                previewImg.src = item.image;
                preview.style.display = 'block';
                document.getElementById('item-image-data').value = item.image;

                // Add remove image button if it doesn't exist
                if (!preview.querySelector('.remove-image-btn')) {
                    const removeBtn = document.createElement('button');
                    removeBtn.type = 'button';
                    removeBtn.className = 'remove-image-btn';
                    removeBtn.innerHTML = '<i class="fas fa-trash"></i> Remove Image';
                    removeBtn.onclick = function() {
                        document.getElementById('item-image-data').value = '';
                        document.getElementById('item-image-preview').style.display = 'none';
                        this.remove();
                    };
                    preview.appendChild(removeBtn);
                }
            }
        }
    }

    // Set the item ID for form submission
    document.getElementById('item-id').value = JSON.stringify({
        category: category || document.getElementById('category-select').value,
        index: (index !== undefined) ? index : -1
    });

    // Show modal
    modal.style.display = 'flex';
}

// Function to save menu item
async function saveMenuItem(e) {
    e.preventDefault();

    const idData = JSON.parse(document.getElementById('item-id').value);
    const category = idData.category;
    const index = parseInt(idData.index);

    // Get image data - either from the file input or keep existing
    let imageData = document.getElementById('item-image-data').value;

    const item = {
        nameEn: document.getElementById('item-name-en').value,
        nameAr: document.getElementById('item-name-ar').value,
        descEn: document.getElementById('item-desc-en').value,
        descAr: document.getElementById('item-desc-ar').value,
        price: document.getElementById('item-price').value,
        image: imageData || '../images/fallback.jpg'
    };

    // Get existing menu items
    let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};

    // Ensure the category exists
    if (!menuItems[category]) {
        menuItems[category] = [];
    }

    if (index === -1) {
        // Add new item
        menuItems[category].push(item);
        showSuccessMessage('New menu item added successfully! Changes will appear on the main website.');
    } else {
        // Update existing item
        menuItems[category][index] = item;
        showSuccessMessage('Menu item updated successfully! Changes will appear on the main website.');
    }

    // Save to Firestore (permanent storage)
    try {
        if (window.firestoreManager) {
            await window.firestoreManager.saveMenuItems(menuItems);
            console.log('Menu items saved to Firestore successfully');
        } else {
            // Fallback to localStorage
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
            console.log('Saved to localStorage as fallback');
        }
    } catch (error) {
        console.error('Error saving to Firestore:', error);
        // Fallback to localStorage
        localStorage.setItem('menuItems', JSON.stringify(menuItems));
        console.log('Saved to localStorage as fallback due to error');
    }

    // Close modal and reload items
    document.getElementById('item-editor-modal').style.display = 'none';
    loadMenuItems();
}

// Function to delete menu item
async function deleteMenuItem(category, index) {
    if (confirm('Are you sure you want to delete this item?')) {
        let menuItems = JSON.parse(localStorage.getItem('menuItems')) || {};
        menuItems[category].splice(index, 1);

        // Save to Firestore (permanent storage)
        try {
            if (window.firestoreManager) {
                await window.firestoreManager.saveMenuItems(menuItems);
                console.log('Menu items updated in Firestore after deletion');
            } else {
                localStorage.setItem('menuItems', JSON.stringify(menuItems));
            }
        } catch (error) {
            console.error('Error updating Firestore after deletion:', error);
            localStorage.setItem('menuItems', JSON.stringify(menuItems));
        }

        showSuccessMessage('Menu item deleted successfully!');
        loadMenuItems();
    }
}

// Function to save all menu changes
function saveMenuChanges() {
    showSuccessMessage('All changes have been saved!');
}

// Function to load settings
function loadSettings() {
    const settings = JSON.parse(localStorage.getItem('settings')) || {};

    if (settings.restaurantNameEn) {
        document.getElementById('restaurant-name-en').value = settings.restaurantNameEn;
    }

    if (settings.restaurantNameAr) {
        document.getElementById('restaurant-name-ar').value = settings.restaurantNameAr;
    }

    if (settings.contactPhone) {
        document.getElementById('contact-phone').value = settings.contactPhone;
    }

    if (settings.contactAddressEn) {
        document.getElementById('contact-address-en').value = settings.contactAddressEn;
    }

    if (settings.contactAddressAr) {
        document.getElementById('contact-address-ar').value = settings.contactAddressAr;
    }
}

// Function to save settings
async function saveSettings(e) {
    e.preventDefault();

    const settings = {
        restaurantNameEn: document.getElementById('restaurant-name-en').value,
        restaurantNameAr: document.getElementById('restaurant-name-ar').value,
        contactPhone: document.getElementById('contact-phone').value,
        contactAddressEn: document.getElementById('contact-address-en').value,
        contactAddressAr: document.getElementById('contact-address-ar').value
    };

    // Save to Firestore (permanent storage)
    try {
        if (window.firestoreManager) {
            await window.firestoreManager.saveSettings(settings);
            console.log('Settings saved to Firestore successfully');
        } else {
            localStorage.setItem('settings', JSON.stringify(settings));
        }
    } catch (error) {
        console.error('Error saving settings to Firestore:', error);
        localStorage.setItem('settings', JSON.stringify(settings));
    }

    showSuccessMessage('Settings saved successfully! Changes will appear on the main website.');
}






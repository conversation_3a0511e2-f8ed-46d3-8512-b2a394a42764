// Set default language
let currentLang = localStorage.getItem('lang') || 'ar';
document.documentElement.lang = currentLang;

// Update active button
document.getElementById('en-btn').classList.remove('active');
document.getElementById('ar-btn').classList.remove('active');
document.getElementById(`${currentLang}-btn`).classList.add('active');

// Language switcher
function switchLanguage(lang) {
    currentLang = lang;
    document.documentElement.lang = lang;
    localStorage.setItem('lang', lang);

    // Update active button
    document.getElementById('en-btn').classList.remove('active');
    document.getElementById('ar-btn').classList.remove('active');
    document.getElementById(`${lang}-btn`).classList.add('active');
}

// Load settings from localStorage
function loadSettings() {
    const settings = JSON.parse(localStorage.getItem('settings'));

    if (settings) {
        // Update restaurant name
        if (settings.restaurantNameEn) {
            document.querySelectorAll('.logo .en').forEach(el => {
                el.textContent = settings.restaurantNameEn;
            });
        }

        if (settings.restaurantNameAr) {
            document.querySelectorAll('.logo .ar').forEach(el => {
                el.textContent = settings.restaurantNameAr;
            });
        }

        // Update contact information
        if (settings.contactPhone) {
            document.querySelectorAll('#contact .fa-phone + p').forEach(el => {
                el.textContent = settings.contactPhone;
            });
        }

        if (settings.contactAddressEn) {
            document.querySelectorAll('#contact .fa-map-marker-alt + p.en').forEach(el => {
                el.textContent = settings.contactAddressEn;
            });
        }

        if (settings.contactAddressAr) {
            document.querySelectorAll('#contact .fa-map-marker-alt + p.ar').forEach(el => {
                el.textContent = settings.contactAddressAr;
            });
        }
    }
}

// Load menu items from Firestore or localStorage
async function loadMenuItems() {
    try {
        // Try to load from Firestore first
        if (window.firebaseDB && window.firestoreFunctions) {
            const docRef = window.firestoreFunctions.doc(window.firebaseDB, 'restaurant', 'menuItems');
            const docSnap = await window.firestoreFunctions.getDoc(docRef);

            if (docSnap.exists()) {
                const data = docSnap.data();
                const menuItems = data.items;
                console.log('Menu items loaded from Firestore');

                // Cache in localStorage
                localStorage.setItem('menuItems', JSON.stringify(menuItems));

                // Display the menu items
                displayMenuItemsFromData(menuItems);
                return;
            }
        }

        // Fallback to localStorage
        const menuItems = JSON.parse(localStorage.getItem('menuItems'));

        if (menuItems) {
            console.log('Menu items loaded from localStorage (fallback)');
            displayMenuItemsFromData(menuItems);
        } else {
            console.log('No menu items found in Firestore or localStorage');
        }

    } catch (error) {
        console.error('Error loading menu items:', error);

        // Final fallback to localStorage
        const menuItems = JSON.parse(localStorage.getItem('menuItems'));
        if (menuItems) {
            displayMenuItemsFromData(menuItems);
        }
    }
}

// Helper function to display menu items from data
function displayMenuItemsFromData(menuItems) {
    if (menuItems) {
        // Loop through each category
        for (const category in menuItems) {
            const categoryContainer = document.querySelector(`.menu-items.${category}`);

            if (categoryContainer && menuItems[category] && menuItems[category].length > 0) {
                // Add items from data
                menuItems[category].forEach(item => {
                    if (item && (item.nameEn || item.nameAr)) {  // Only add valid items
                        // Fix image path if it's from admin
                        let imagePath = item.image;
                        if (imagePath && imagePath.startsWith('../')) {
                            imagePath = imagePath.substring(3); // Remove '../' prefix
                        }

                        // Check if this item already exists by name
                        const existingItem = Array.from(categoryContainer.querySelectorAll('.menu-item')).find(el => {
                            const nameEl = el.querySelector('.menu-item-info h3.en');
                            return nameEl && nameEl.textContent === item.nameEn;
                        });

                        // Only add if it doesn't exist
                        if (!existingItem) {
                            const menuItemElement = document.createElement('div');
                            menuItemElement.className = 'menu-item';

                            menuItemElement.innerHTML = `
                                <div class="menu-item-info">
                                    <h3 class="en">${item.nameEn || ''}</h3>
                                    <h3 class="ar">${item.nameAr || ''}</h3>
                                    <p class="en">${item.descEn || ''}</p>
                                    <p class="ar">${item.descAr || ''}</p>
                                    <span class="price">${item.price || 0} SAR</span>
                                </div>
                                <div class="menu-item-image" style="background-image: url('${imagePath || 'images/fallback.jpg'}')"></div>
                            `;
                            categoryContainer.appendChild(menuItemElement);
                        }
                    }
                });
            }
        }
    }
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
            window.scrollTo({
                top: targetElement.offsetTop - 70, // Offset for header
                behavior: 'smooth'
            });
        }
    });
});

// Menu tabs functionality
function showCategory(category) {
    // Hide all menu items
    const menuItems = document.querySelectorAll('.menu-items');
    menuItems.forEach(item => {
        item.style.display = 'none';
    });

    // Show selected category
    document.querySelector(`.${category}`).style.display = 'grid';

    // Update active tab
    const tabs = document.querySelectorAll('.menu-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });

    event.currentTarget.classList.add('active');
}

// Responsive navigation
window.addEventListener('resize', function() {
    // Reset slider position on window resize
    currentIndex = 0;
    slider.style.transform = 'translateX(0)';
});

// Firebase Authentication Functions
function showLoginModal() {
    document.getElementById('login-modal').style.display = 'flex';
}

function hideLoginModal() {
    document.getElementById('login-modal').style.display = 'none';
    document.getElementById('login-error-message').textContent = '';
    document.getElementById('firebase-login-form').reset();
}

// Handle Firebase login
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadMenuItems();

    // Add login form handler
    const loginForm = document.getElementById('firebase-login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;
            const errorMessage = document.getElementById('login-error-message');

            try {
                console.log('Attempting to sign in with email:', email);

                // Check if Firebase auth is available
                if (!window.firebaseAuth || !window.signInWithEmailAndPassword) {
                    throw new Error('Firebase authentication not initialized');
                }

                // Sign in with Firebase
                const userCredential = await window.signInWithEmailAndPassword(window.firebaseAuth, email, password);
                console.log('Login successful:', userCredential.user.email);

                // Success - modal will be hidden by auth state change
                hideLoginModal();

            } catch (error) {
                console.error('Login error details:', error);
                console.error('Error code:', error.code);
                console.error('Error message:', error.message);

                // Show user-friendly error messages
                let errorText = 'Login failed. Please try again.';

                switch (error.code) {
                    case 'auth/user-not-found':
                        errorText = 'No admin account found with this email address. Please contact the administrator.';
                        break;
                    case 'auth/wrong-password':
                        errorText = 'Incorrect password. Please try again.';
                        break;
                    case 'auth/invalid-email':
                        errorText = 'Please enter a valid email address.';
                        break;
                    case 'auth/user-disabled':
                        errorText = 'This admin account has been disabled.';
                        break;
                    case 'auth/too-many-requests':
                        errorText = 'Too many failed attempts. Please try again later.';
                        break;
                    case 'auth/network-request-failed':
                        errorText = 'Network error. Please check your internet connection.';
                        break;
                    case 'auth/invalid-credential':
                        errorText = 'Invalid email or password. Please check your credentials.';
                        break;
                    default:
                        errorText = `Login failed: ${error.message}`;
                        break;
                }

                errorMessage.textContent = errorText;
            }
        });
    }

    // Close modal when clicking outside
    document.getElementById('login-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideLoginModal();
        }
    });
});

// Admin logout function
function adminLogout() {
    if (window.firebaseAuth && window.signOut) {
        window.signOut(window.firebaseAuth).then(() => {
            console.log('Admin logged out successfully');
        }).catch((error) => {
            console.error('Logout error:', error);
        });
    }
}

// Debug function to test Firebase connection
function testFirebaseConnection() {
    console.log('=== Firebase Debug Info ===');
    console.log('Firebase Auth object:', window.firebaseAuth);
    console.log('SignIn function:', window.signInWithEmailAndPassword);
    console.log('Firebase config loaded:', !!window.firebaseAuth);

    if (window.firebaseAuth) {
        console.log('Current user:', window.firebaseAuth.currentUser);
        console.log('Auth state ready');
    } else {
        console.error('Firebase Auth not initialized!');
    }
    console.log('=== End Debug Info ===');
}

// Make test function available globally
window.testFirebaseConnection = testFirebaseConnection;














// Restaurant Configuration - Central Variable System
// All changeable content is controlled through these variables

window.RestaurantConfig = {
    // Restaurant Information
    restaurant: {
        nameEn: 'Al-Andalus Restaurant',
        nameAr: 'مطعم الأندلس',
        phone: '+966 12 534 0000',
        addressEn: 'Ibrahim <PERSON>halil Street, Al Haram, Makkah 21955, Saudi Arabia',
        addressAr: 'شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية'
    },

    // Menu Items - All categories
    menu: {
        appetizers: [
            {
                nameEn: "Hummus",
                nameAr: "حمص",
                descEn: "Creamy chickpea dip with tahini and olive oil",
                descAr: "غموس الحمص الكريمي مع الطحينة وزيت الزيتون",
                price: "15",
                image: "images/hummus.JPG"
            },
            {
                nameEn: "<PERSON>",
                nameAr: "بابا غنوج",
                descEn: "Smoky roasted eggplant dip with garlic and lemon",
                descAr: "غموس الباذنجان المشوي المدخن مع الثوم والليمون",
                price: "18",
                image: "images/baba ghanoush.jpg"
            },
            {
                nameEn: "Muhammara",
                nameAr: "محمرة",
                descEn: "Spicy red pepper and walnut dip",
                descAr: "غموس الفلفل الأحمر والجوز الحار",
                price: "20",
                image: "images/muhammara.jpg"
            },
            {
                nameEn: "Kibbeh",
                nameAr: "كبة",
                descEn: "Deep-fried bulgur and meat croquettes",
                descAr: "كروكيت البرغل واللحم المقلي",
                price: "25",
                image: "images/kibbeh.jpg"
            },
            {
                nameEn: "Warak Enab",
                nameAr: "ورق عنب",
                descEn: "Stuffed grape leaves with rice and herbs",
                descAr: "ورق العنب المحشو بالأرز والأعشاب",
                price: "22",
                image: "images/warak enab.jpg"
            },
            {
                nameEn: "Makdous",
                nameAr: "مكدوس",
                descEn: "Pickled baby eggplants stuffed with walnuts",
                descAr: "باذنجان صغير مخلل محشو بالجوز",
                price: "18",
                image: "images/makdous.jpg"
            },
            {
                nameEn: "Sambousek",
                nameAr: "سمبوسك",
                descEn: "Crispy pastries filled with cheese or meat",
                descAr: "معجنات مقرمشة محشوة بالجبن أو اللحم",
                price: "20",
                image: "images/sambousek.jpg"
            },
            {
                nameEn: "Labneh",
                nameAr: "لبنة",
                descEn: "Thick strained yogurt served with olive oil",
                descAr: "لبن مصفى كثيف يُقدم مع زيت الزيتون",
                price: "16",
                image: "images/labneh.jpg"
            },
            {
                nameEn: "Zaatar Bread",
                nameAr: "خبز زعتر",
                descEn: "Fresh bread topped with zaatar and olive oil",
                descAr: "خبز طازج مغطى بالزعتر وزيت الزيتون",
                price: "12",
                image: "images/zaatar.jpg"
            }
        ],
        main: [
            {
                nameEn: "Beef Kebab",
                nameAr: "كباب لحم",
                descEn: "Grilled beef skewers with Middle Eastern spices",
                descAr: "أسياخ لحم البقر المشوية مع التوابل الشرق أوسطية",
                price: "55",
                image: "images/beef kebab.jpg"
            },
            {
                nameEn: "Chicken Kabsa",
                nameAr: "كبسة دجاج",
                descEn: "Traditional Saudi rice dish with spiced chicken",
                descAr: "طبق الأرز السعودي التقليدي مع الدجاج المتبل",
                price: "45",
                image: "images/chicken-kabsa.jpg"
            },
            {
                nameEn: "Shish Tawook",
                nameAr: "شيش طاووق",
                descEn: "Marinated chicken skewers grilled to perfection",
                descAr: "أسياخ الدجاج المتبلة والمشوية للكمال",
                price: "42",
                image: "images/shish-tawook.jpg"
            },
            {
                nameEn: "Mixed Grill Platter",
                nameAr: "طبق مشاوي مشكلة",
                descEn: "Assorted grilled meats with rice and vegetables",
                descAr: "تشكيلة من اللحوم المشوية مع الأرز والخضار",
                price: "65",
                image: "images/mixed-grill-platter.jpg"
            },
            {
                nameEn: "Lamb Chops",
                nameAr: "ريش خروف",
                descEn: "Tender grilled lamb chops with herbs",
                descAr: "ريش الخروف المشوية الطرية مع الأعشاب",
                price: "75",
                image: "images/lamb-chops.jpg"
            },
            {
                nameEn: "Lahm Maklouba",
                nameAr: "لحم مقلوبة",
                descEn: "Traditional upside-down rice dish with meat",
                descAr: "طبق الأرز المقلوب التقليدي مع اللحم",
                price: "58",
                image: "images/lahm-maklouba.jpg"
            },
            {
                nameEn: "Neapolitan Pizza",
                nameAr: "بيتزا نابولي",
                descEn: "Authentic Italian pizza with fresh ingredients",
                descAr: "بيتزا إيطالية أصيلة مع مكونات طازجة",
                price: "48",
                image: "images/neapolitan-pizza.jpg"
            },
            {
                nameEn: "Vegetable Pizza",
                nameAr: "بيتزا خضروات",
                descEn: "Pizza with fresh vegetables",
                descAr: "بيتزا مع خضروات طازجة",
                price: "50",
                image: "images/vegetable-pizza.jpg"
            },
            {
                nameEn: "Chicken Shawarma",
                nameAr: "شاورما دجاج",
                descEn: "Marinated chicken wrapped in pita bread",
                descAr: "دجاج متبل ملفوف في خبز البيتا",
                price: "25",
                image: "images/chicken-shawarma.jpg"
            }
        ],
        salads: [
            {
                nameEn: "Tabbouleh",
                nameAr: "تبولة",
                descEn: "Fresh parsley salad with bulgur, tomatoes, and mint",
                descAr: "سلطة البقدونس الطازجة مع البرغل والطماطم والنعناع",
                price: "22",
                image: "images/tabbouleh.jpg"
            },
            {
                nameEn: "Caesar Salad",
                nameAr: "سلطة سيزر",
                descEn: "Crisp romaine lettuce with Caesar dressing and croutons",
                descAr: "خس روماني مقرمش مع تتبيلة السيزر والخبز المحمص",
                price: "28",
                image: "images/caesar-salad.jpg"
            },
            {
                nameEn: "Fattoush",
                nameAr: "فتوش",
                descEn: "Mixed greens with crispy bread and sumac dressing",
                descAr: "خضار مشكلة مع الخبز المقرمش وتتبيلة السماق",
                price: "25",
                image: "images/fattoush.jpg"
            },
            {
                nameEn: "Greek Salad",
                nameAr: "سلطة يونانية",
                descEn: "Fresh vegetables with feta cheese and olives",
                descAr: "خضروات طازجة مع جبن الفيتا والزيتون",
                price: "26",
                image: "images/greek-salad.jpg"
            }
        ],
        soups: [
            {
                nameEn: "Lentil Soup",
                nameAr: "شوربة عدس",
                descEn: "Traditional Middle Eastern lentil soup",
                descAr: "شوربة العدس الشرق أوسطية التقليدية",
                price: "18",
                image: "images/lentil-soup.jpg"
            },
            {
                nameEn: "Chicken Soup",
                nameAr: "شوربة دجاج",
                descEn: "Hearty chicken soup with vegetables",
                descAr: "شوربة دجاج مغذية مع الخضروات",
                price: "20",
                image: "images/chicken-soup.jpg"
            },
            {
                nameEn: "Mushroom Soup",
                nameAr: "شوربة فطر",
                descEn: "Creamy mushroom soup with herbs",
                descAr: "شوربة فطر كريمية مع الأعشاب",
                price: "22",
                image: "images/mushroom-soup.jpg"
            },
            {
                nameEn: "Tomato Soup",
                nameAr: "شوربة طماطم",
                descEn: "Rich tomato soup with fresh basil",
                descAr: "شوربة طماطم غنية مع الريحان الطازج",
                price: "16",
                image: "images/tomato-soup.jpg"
            },
            {
                nameEn: "Vegetable Soup",
                nameAr: "شوربة خضار",
                descEn: "A healthy mix of seasonal vegetables in a light broth",
                descAr: "مزيج صحي من الخضروات الموسمية في مرق خفيف",
                price: "18",
                image: "images/vegetable-soup.jpg"
            }
        ],
        desserts: [
            {
                nameEn: "Kunafa",
                nameAr: "كنافة",
                descEn: "Traditional sweet cheese pastry soaked in sweet syrup",
                descAr: "حلوى تقليدية من الجبن والعجين المغمور بالقطر الحلو",
                price: "30",
                image: "images/desserts/kunafa.jpg"
            },
            {
                nameEn: "Ice Cream",
                nameAr: "آيس كريم",
                descEn: "Smooth and creamy ice cream in a variety of flavors",
                descAr: "آيس كريم ناعم وكريمي بنكهات متنوعة",
                price: "22",
                image: "images/desserts/ice-cream.jpg"
            },
            {
                nameEn: "Mahalabiya",
                nameAr: "مهلبية",
                descEn: "A silky milk pudding flavored with rose or orange blossom water",
                descAr: "بودينغ الحليب الناعم بنكهات ماء الورد أو الزهر",
                price: "32",
                image: "images/desserts/mahalabiya.jpg"
            },
            {
                nameEn: "Tiramisu",
                nameAr: "تيراميسو",
                descEn: "Classic coffee-flavored dessert with creamy mascarpone",
                descAr: "تحلية تقليدية بنكهة القهوة مع كريمة الماسكاربوني",
                price: "35",
                image: "images/desserts/tiramisu.jpg"
            },
            {
                nameEn: "Ma'amoul",
                nameAr: "معمول",
                descEn: "Traditional shortbread cookies stuffed with dates or nuts",
                descAr: "كوكيز تقليدية محشوة بالتمر أو المكسرات",
                price: "20",
                image: "images/desserts/maamoul.jpg"
            },
            {
                nameEn: "Baklava",
                nameAr: "بقلاوة",
                descEn: "Layered pastry filled with chopped nuts and sweetened with syrup",
                descAr: "حلوى من طبقات العجين المحشوة بالمكسرات والمحلاة بالقطر",
                price: "25",
                image: "images/desserts/baklava.jpg"
            },
            {
                nameEn: "Cheesecake",
                nameAr: "تشيز كيك",
                descEn: "A velvety smooth cheesecake with a buttery biscuit base",
                descAr: "كعكة الجبن الناعمة المخملية مع قاعدة بسكويت زبدية",
                price: "35",
                image: "images/desserts/cheesecake.jpg"
            },
            {
                nameEn: "Umm Ali",
                nameAr: "أم علي",
                descEn: "A warm Egyptian bread pudding with milk, nuts, and coconut",
                descAr: "بودينغ خبز مصري دافئ مع الحليب والمكسرات وجوز الهند",
                price: "25",
                image: "images/desserts/umm ali.jpg"
            },
            {
                nameEn: "Qatayef",
                nameAr: "قطايف",
                descEn: "A sweet stuffed pancake filled with nuts or cream",
                descAr: "فطائر محشوة بالمكسرات أو القشطة",
                price: "28",
                image: "images/desserts/qatayef.jpg"
            },
            {
                nameEn: "Basbousa",
                nameAr: "بسبوسة",
                descEn: "Sweet semolina cake soaked in syrup",
                descAr: "كعكة السميد الحلوة المنقوعة في القطر",
                price: "24",
                image: "images/desserts/basbousa.jpg"
            }
        ],
        "cold-beverages": [
            {
                nameEn: "Fresh Orange Juice",
                nameAr: "عصير برتقال طازج",
                descEn: "Freshly squeezed orange juice",
                descAr: "عصير برتقال طازج معصور",
                price: "15",
                image: "images/orange-juice.jpg"
            },
            {
                nameEn: "Mango Juice",
                nameAr: "عصير مانجو",
                descEn: "Fresh mango juice",
                descAr: "عصير مانجو طازج",
                price: "16",
                image: "images/mango-juice.jpg"
            },
            {
                nameEn: "Guava Juice",
                nameAr: "عصير جوافة",
                descEn: "Fresh guava juice",
                descAr: "عصير جوافة طازج",
                price: "16",
                image: "images/guava-juice.jpg"
            },
            {
                nameEn: "Strawberry Smoothie",
                nameAr: "سموذي فراولة",
                descEn: "Creamy strawberry smoothie",
                descAr: "سموذي فراولة كريمي",
                price: "18",
                image: "images/strawberry-smoothie.jpg"
            },
            {
                nameEn: "Iced Coffee",
                nameAr: "قهوة مثلجة",
                descEn: "Cold brew coffee served over ice",
                descAr: "قهوة باردة تُقدم مع الثلج",
                price: "14",
                image: "images/iced-coffee.jpg"
            }
        ],
        "hot-beverages": [
            {
                nameEn: "Arabic Coffee",
                nameAr: "قهوة عربية",
                descEn: "Traditional Arabic coffee with cardamom",
                descAr: "القهوة العربية التقليدية مع الهيل",
                price: "12",
                image: "images/arabic-coffee.jpg"
            },
            {
                nameEn: "Turkish Coffee",
                nameAr: "قهوة تركية",
                descEn: "Strong traditional Turkish coffee",
                descAr: "قهوة تركية تقليدية قوية",
                price: "14",
                image: "images/turkish-coffee.jpg"
            },
            {
                nameEn: "Black Tea",
                nameAr: "شاي أسود",
                descEn: "Classic black tea",
                descAr: "شاي أسود كلاسيكي",
                price: "8",
                image: "images/black-tea.jpg"
            },
            {
                nameEn: "Green Tea",
                nameAr: "شاي أخضر",
                descEn: "Refreshing green tea",
                descAr: "شاي أخضر منعش",
                price: "10",
                image: "images/green-tea.jpg"
            },
            {
                nameEn: "Hot Chocolate",
                nameAr: "شوكولاتة ساخنة",
                descEn: "Rich hot chocolate with whipped cream",
                descAr: "شوكولاتة ساخنة غنية مع الكريمة المخفوقة",
                price: "16",
                image: "images/hot-chocolate.jpg"
            }
        ],
        extras: [
            {
                nameEn: "French Fries",
                nameAr: "بطاطس مقلية",
                descEn: "Crispy golden french fries",
                descAr: "بطاطس مقلية ذهبية مقرمشة",
                price: "12",
                image: "images/french-fries.jpg"
            },
            {
                nameEn: "BBQ Sauce",
                nameAr: "صوص باربكيو",
                descEn: "Tangy barbecue sauce",
                descAr: "صوص باربكيو لذيذ",
                price: "5",
                image: "images/bbq-sauce.jpg"
            },
            {
                nameEn: "Cheese Sauce",
                nameAr: "صوص جبن",
                descEn: "Creamy cheese sauce",
                descAr: "صوص جبن كريمي",
                price: "6",
                image: "images/cheese-sauce.jpg"
            },
            {
                nameEn: "Honey Mustard Sauce",
                nameAr: "صوص خردل بالعسل",
                descEn: "Sweet and tangy honey mustard",
                descAr: "خردل بالعسل حلو ولذيذ",
                price: "5",
                image: "images/honey-mustard-sauce.jpg"
            },
            {
                nameEn: "Ketchup",
                nameAr: "كاتشب",
                descEn: "Classic tomato ketchup",
                descAr: "كاتشب طماطم كلاسيكي",
                price: "3",
                image: "images/ketchup.jpg"
            },
            {
                nameEn: "Mayonnaise",
                nameAr: "مايونيز",
                descEn: "Creamy mayonnaise",
                descAr: "مايونيز كريمي",
                price: "4",
                image: "images/mayonnaise.jpg"
            },
            {
                nameEn: "Tahini",
                nameAr: "طحينة",
                descEn: "Traditional sesame seed paste",
                descAr: "معجون السمسم التقليدي",
                price: "6",
                image: "images/tahini.jpg"
            }
        ]
    },

    // Admin Configuration
    admin: {
        emails: [
            '<EMAIL>',
            '<EMAIL>'
        ]
    },

    // Save configuration to localStorage
    save: function() {
        localStorage.setItem('restaurantConfig', JSON.stringify(this));
        console.log('Configuration saved to localStorage');
    },

    // Load configuration from localStorage
    load: function() {
        const saved = localStorage.getItem('restaurantConfig');
        if (saved) {
            const config = JSON.parse(saved);
            // Merge saved config with current config
            Object.assign(this.restaurant, config.restaurant || {});
            Object.assign(this.menu, config.menu || {});
            Object.assign(this.admin, config.admin || {});
            console.log('Configuration loaded from localStorage');
        }
        return this;
    },

    // Update restaurant info
    updateRestaurant: function(newInfo) {
        Object.assign(this.restaurant, newInfo);
        this.save();
        this.updateUI();
    },

    // Add menu item
    addMenuItem: function(category, item) {
        if (!this.menu[category]) {
            this.menu[category] = [];
        }
        this.menu[category].push(item);
        this.save();
        this.updateUI();
    },

    // Update menu item
    updateMenuItem: function(category, index, item) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category][index] = item;
            this.save();
            this.updateUI();
        }
    },

    // Delete menu item
    deleteMenuItem: function(category, index) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category].splice(index, 1);
            this.save();
            this.updateUI();
        }
    },

    // Update UI elements
    updateUI: function() {
        // Update restaurant name in header if exists
        const headerNameEn = document.querySelector('.restaurant-name.en');
        const headerNameAr = document.querySelector('.restaurant-name.ar');
        if (headerNameEn) headerNameEn.textContent = this.restaurant.nameEn;
        if (headerNameAr) headerNameAr.textContent = this.restaurant.nameAr;

        // Update contact info if exists
        const phoneEl = document.querySelector('.contact-phone');
        if (phoneEl) phoneEl.textContent = this.restaurant.phone;

        // Trigger menu reload if function exists
        if (typeof loadMenuItems === 'function') {
            loadMenuItems();
        }
        if (typeof displayMenuItemsFromData === 'function') {
            displayMenuItemsFromData(this.menu);
        }
    },

    // Reset to default configuration
    reset: function() {
        localStorage.removeItem('restaurantConfig');
        localStorage.removeItem('menuItems');
        localStorage.removeItem('settings');
        location.reload();
    }
};

// Initialize configuration on load
window.RestaurantConfig.load();

// Force update if this is a new version
if (!localStorage.getItem('configVersion') || localStorage.getItem('configVersion') !== '2.0') {
    console.log('Updating to new configuration version...');
    localStorage.removeItem('restaurantConfig');
    localStorage.removeItem('menuItems');
    localStorage.setItem('configVersion', '2.0');
    window.RestaurantConfig.save();
}

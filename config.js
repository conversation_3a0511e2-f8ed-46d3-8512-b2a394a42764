// Restaurant Configuration - Central Variable System
// All changeable content is controlled through these variables

window.RestaurantConfig = {
    // Restaurant Information
    restaurant: {
        nameEn: 'Al-Andalus Restaurant',
        nameAr: 'مطعم الأندلس',
        phone: '+966 12 534 0000',
        addressEn: 'Ibrahim <PERSON>halil Street, Al Haram, Makkah 21955, Saudi Arabia',
        addressAr: 'شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية'
    },

    // Menu Items - All categories
    menu: {
        appetizers: [
            {
                nameEn: "Hummus",
                nameAr: "حمص",
                descEn: "Creamy chickpea dip with tahini, olive oil, and spices",
                descAr: "غموس الحمص الكريمي مع الطحينة وزيت الزيتون والتوابل",
                price: "15",
                image: "images/hummus.JPG"
            },
            {
                nameEn: "Baba Ghanoush",
                nameAr: "بابا غنوج",
                descEn: "Smoky roasted eggplant dip with garlic and lemon",
                descAr: "غموس الباذنجان المشوي المدخن مع الثوم والليمون",
                price: "18",
                image: "images/baba ghanoush.jpg"
            },
            {
                nameEn: "Falafel",
                nameAr: "فلافل",
                descEn: "Crispy fried chickpea balls with herbs and spices",
                descAr: "كرات الحمص المقلية المقرمشة مع الأعشاب والتوابل",
                price: "20",
                image: "images/falafel.jpg"
            }
        ],
        main: [
            {
                nameEn: "Chicken Kabsa",
                nameAr: "كبسة دجاج",
                descEn: "Traditional Saudi rice dish with spiced chicken",
                descAr: "طبق الأرز السعودي التقليدي مع الدجاج المتبل",
                price: "45",
                image: "images/chicken-kabsa.jpg"
            },
            {
                nameEn: "Mixed Grill Platter",
                nameAr: "طبق مشاوي مشكلة",
                descEn: "Assorted grilled meats with rice and vegetables",
                descAr: "تشكيلة من اللحوم المشوية مع الأرز والخضار",
                price: "65",
                image: "images/mixed-grill-platter.jpg"
            },
            {
                nameEn: "Lamb Chops",
                nameAr: "ريش خروف",
                descEn: "Tender grilled lamb chops with herbs",
                descAr: "ريش الخروف المشوية الطرية مع الأعشاب",
                price: "75",
                image: "images/lamb-chops.jpg"
            }
        ],
        seafood: [
            {
                nameEn: "Grilled Fish",
                nameAr: "سمك مشوي",
                descEn: "Fresh fish grilled with Middle Eastern spices",
                descAr: "سمك طازج مشوي مع التوابل الشرق أوسطية",
                price: "55",
                image: "images/fallback.jpg"
            }
        ],
        salads: [
            {
                nameEn: "Tabbouleh",
                nameAr: "تبولة",
                descEn: "Fresh parsley salad with tomatoes, mint, and bulgur",
                descAr: "سلطة البقدونس الطازجة مع الطماطم والنعناع والبرغل",
                price: "25",
                image: "images/tabbouleh.jpg"
            },
            {
                nameEn: "Fattoush",
                nameAr: "فتوش",
                descEn: "Mixed greens with crispy bread and sumac dressing",
                descAr: "خضار مشكلة مع الخبز المقرمش وتتبيلة السماق",
                price: "28",
                image: "images/fattoush.jpg"
            }
        ],
        soups: [
            {
                nameEn: "Lentil Soup",
                nameAr: "شوربة عدس",
                descEn: "Traditional Middle Eastern lentil soup",
                descAr: "شوربة العدس الشرق أوسطية التقليدية",
                price: "18",
                image: "images/lentil-soup.jpg"
            }
        ],
        sandwiches: [
            {
                nameEn: "Chicken Shawarma",
                nameAr: "شاورما دجاج",
                descEn: "Marinated chicken wrapped in pita bread",
                descAr: "دجاج متبل ملفوف في خبز البيتا",
                price: "25",
                image: "images/chicken-shawarma.jpg"
            }
        ],
        desserts: [
            {
                nameEn: "Baklava",
                nameAr: "بقلاوة",
                descEn: "Layered pastry with nuts and honey",
                descAr: "معجنات مطبقة مع المكسرات والعسل",
                price: "20",
                image: "images/desserts/baklava.jpg"
            },
            {
                nameEn: "Kunafa",
                nameAr: "كنافة",
                descEn: "Sweet cheese pastry with crispy shredded dough",
                descAr: "معجنات الجبن الحلوة مع العجين المبشور المقرمش",
                price: "25",
                image: "images/desserts/kunafa.jpg"
            }
        ],
        beverages: [
            {
                nameEn: "Arabic Coffee",
                nameAr: "قهوة عربية",
                descEn: "Traditional Arabic coffee with cardamom",
                descAr: "القهوة العربية التقليدية مع الهيل",
                price: "12",
                image: "images/arabic-coffee.jpg"
            },
            {
                nameEn: "Fresh Orange Juice",
                nameAr: "عصير برتقال طازج",
                descEn: "Freshly squeezed orange juice",
                descAr: "عصير برتقال طازج معصور",
                price: "15",
                image: "images/orange-juice.jpg"
            }
        ],
        breakfast: [
            {
                nameEn: "Labneh Plate",
                nameAr: "طبق لبنة",
                descEn: "Creamy strained yogurt with olive oil and herbs",
                descAr: "لبنة كريمية مع زيت الزيتون والأعشاب",
                price: "22",
                image: "images/labneh.jpg"
            }
        ],
        specials: [
            {
                nameEn: "Chef's Special Kabsa",
                nameAr: "كبسة الشيف الخاصة",
                descEn: "Premium rice dish with lamb and special spice blend",
                descAr: "طبق أرز فاخر مع لحم ضأن وخلطة توابل خاصة",
                price: "90",
                image: "images/special-grill-platter.jpg"
            }
        ]
    },

    // Admin Configuration
    admin: {
        emails: [
            '<EMAIL>',
            '<EMAIL>'
        ]
    },

    // Save configuration to localStorage
    save: function() {
        localStorage.setItem('restaurantConfig', JSON.stringify(this));
        console.log('Configuration saved to localStorage');
    },

    // Load configuration from localStorage
    load: function() {
        const saved = localStorage.getItem('restaurantConfig');
        if (saved) {
            const config = JSON.parse(saved);
            // Merge saved config with current config
            Object.assign(this.restaurant, config.restaurant || {});
            Object.assign(this.menu, config.menu || {});
            Object.assign(this.admin, config.admin || {});
            console.log('Configuration loaded from localStorage');
        }
        return this;
    },

    // Update restaurant info
    updateRestaurant: function(newInfo) {
        Object.assign(this.restaurant, newInfo);
        this.save();
        this.updateUI();
    },

    // Add menu item
    addMenuItem: function(category, item) {
        if (!this.menu[category]) {
            this.menu[category] = [];
        }
        this.menu[category].push(item);
        this.save();
        this.updateUI();
    },

    // Update menu item
    updateMenuItem: function(category, index, item) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category][index] = item;
            this.save();
            this.updateUI();
        }
    },

    // Delete menu item
    deleteMenuItem: function(category, index) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category].splice(index, 1);
            this.save();
            this.updateUI();
        }
    },

    // Update UI elements
    updateUI: function() {
        // Update restaurant name in header if exists
        const headerNameEn = document.querySelector('.restaurant-name.en');
        const headerNameAr = document.querySelector('.restaurant-name.ar');
        if (headerNameEn) headerNameEn.textContent = this.restaurant.nameEn;
        if (headerNameAr) headerNameAr.textContent = this.restaurant.nameAr;

        // Update contact info if exists
        const phoneEl = document.querySelector('.contact-phone');
        if (phoneEl) phoneEl.textContent = this.restaurant.phone;

        // Trigger menu reload if function exists
        if (typeof loadMenuItems === 'function') {
            loadMenuItems();
        }
        if (typeof displayMenuItemsFromData === 'function') {
            displayMenuItemsFromData(this.menu);
        }
    }
};

// Initialize configuration on load
window.RestaurantConfig.load();
